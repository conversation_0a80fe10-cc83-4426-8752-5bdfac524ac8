name: ci

on: [push, pull_request]

jobs:
  npm:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [10.x, 12.x]
        os: [ubuntu-latest, ubuntu-18.04, ubuntu-16.04, windows-latest, macOS-latest]

    steps:
      - uses: actions/checkout@v1

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install with npm
        run: |
          npm install
      - name: Run tests
        run: |
          npm run test

  yarn:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [10.x, 12.x]
        os: [ubuntu-latest, ubuntu-18.04, ubuntu-16.04, windows-latest, macOS-latest]

    steps:
      - uses: actions/checkout@v1

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install with yarn
        run: |
          curl -o- -L https://yarnpkg.com/install.sh | bash
          yarn install
      - name: Run tests
        run: |
          yarn run test
