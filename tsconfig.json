{"compilerOptions": {"moduleResolution": "node", "declaration": true, "target": "es2016", "module": "commonjs", "outDir": "lib", "pretty": true, "noEmitOnError": true, "experimentalDecorators": true, "sourceMap": true, "emitDecoratorMetadata": true, "strict": true, "noImplicitAny": false, "esModuleInterop": true, "removeComments": true, "noUnusedLocals": true, "lib": ["esnext"], "typeRoots": ["./node_modules/@types", "./typings"]}, "formatCodeOptions": {"identSize": 2, "tabSize": 2}, "exclude": ["node_modules"], "include": ["./src/**/*.ts"]}