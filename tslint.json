{"extends": ["tslint-config-prettier"], "rules": {"adjacent-overload-signatures": true, "curly": true, "eofline": false, "class-name": true, "no-angle-bracket-type-assertion": true, "max-file-line-count": [true, 750], "no-debugger": true, "prefer-const": true, "no-empty-interface": true, "no-string-throw": true, "unified-signatures": true, "prefer-method-signature": true, "arrow-return-shorthand": [true, "multiline"], "no-duplicate-variable": true, "no-inferrable-types": [true, "ignore-params"], "no-var-keyword": true, "variable-name": [true, "ban-keywords", "check-format", "allow-leading-underscore", "allow-pascal-case"], "no-empty": false, "no-shadowed-variable": true, "no-unused-expression": true, "triple-equals": true, "jsdoc-format": true, "semicolon": [false, "always"]}}