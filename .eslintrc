{
    "env": {
        "es6": true,
        "node": true
    },
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "project": "tsconfig.json",
        "sourceType": "module"
    },
    "plugins": [
        "eslint-plugin-jsdoc",
        "eslint-plugin-react",
        "@typescript-eslint",
        "@typescript-eslint/tslint",
        "@stylistic"
    ],
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:@typescript-eslint/eslint-recommended",
        "plugin:@typescript-eslint/recommended",
        // "plugin:@typescript-eslint/recommended-requiring-type-checking",
        // "plugin:@typescript-eslint/tslint",
        "plugin:@stylistic/disable-legacy",
        "plugin:@stylistic/recommended",
        // "plugin:@stylistic/recommended-extends",
        "plugin:oxlint/recommended"
    ],
    "root": true,
    "ignorePatterns": ["biome.json", ".oxlintrc.json", "eslint.config.js", ".eslintrc", "tsconfig.json", "tslint.json"],
    "rules": {
        "@typescript-eslint/adjacent-overload-signatures": "error",
        "@typescript-eslint/consistent-type-assertions": "error",
        "@typescript-eslint/indent": "off",
        "@typescript-eslint/member-delimiter-style": [
            "off",
            {
                "multiline": {
                    "delimiter": "semi",
                    "requireLast": true
                },
                "singleline": {
                    "delimiter": "semi",
                    "requireLast": false
                }
            }
        ],
        "@typescript-eslint/naming-convention": [
            "error",
            {
                "selector": "variable",
                "format": [
                    "camelCase",
                    "strictCamelCase",
                    "UPPER_CASE",
                    "PascalCase",
                    "StrictPascalCase",
                    "snake_case"
                ],
                "leadingUnderscore": "allow",
                "trailingUnderscore": "forbid"
            }
        ],
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-empty-interface": "error",
        "@typescript-eslint/no-inferrable-types": [
            "error",
            {
                "ignoreParameters": true
            }
        ],
        "@typescript-eslint/no-shadow": [
            "error",
            {
                "hoist": "all"
            }
        ],
        "@typescript-eslint/no-unused-expressions": "error",
        "@typescript-eslint/quotes": "off",
        /* "@typescript-eslint/semi": [
            "off",
            "always"
        ], */
        "@typescript-eslint/semi": "off",
        "@typescript-eslint/type-annotation-spacing": "off",
        "@typescript-eslint/unified-signatures": "error",
        /* "arrow-body-style": [
            "error",
            "always"
        ], */
        "arrow-body-style": "off",
        "@stylistic/arrow-body-style": ["error", "as-needed"],
        /* "arrow-parens": [
            "off",
            "always"
        ], */
        "arrow-parens": "off",
        "@stylistic/arrow-parens": ["error", "always"],
        /* "brace-style": [
            "off",
            "off"
        ], */
        "brace-style": "off",
        "@stylistic/js/brace-style": [
            "off",
            "off"
        ],
        "comma-dangle": "off",
        "@stylistic/comma-dangle": "off",
        "curly": "error",
        "eol-last": "off",
        "@stylistic/eol-last": "off",
        "eqeqeq": [
            "error",
            "always"
        ],
        "id-denylist": [
            "error",
            "any",
            "Number",
            "number",
            "String",
            "string",
            "Boolean",
            "boolean",
            "Undefined",
            "undefined"
        ],
        "id-match": "error",
        "indent": "off",
        "@stylistic/indent": "off",
        "jsdoc/check-alignment": "error",
        "jsdoc/check-indentation": "error",
        "jsdoc/newline-after-description": "error",
        "linebreak-style": "off",
        "@stylistic/linebreak-style": "off",
        "max-len": "off",
        "@stylistic/max-len": "off",
        "max-lines": [
            "error",
            750
        ],
        "new-parens": "off",
        "@stylistic/new-parens": "off",
        "newline-per-chained-call": "off",
        "@stylistic/newline-per-chained-call": "off",
        "no-debugger": "error",
        "no-empty": "off",
        "no-empty-function": "off",
        "no-extra-semi": "off",
        "@stylistic/no-extra-semi": "off",
        "no-irregular-whitespace": "off",
        "no-multiple-empty-lines": "off",
        "no-redeclare": "error",
        "no-shadow": "off",
        "no-throw-literal": "error",
        "no-trailing-spaces": "off",
        "no-underscore-dangle": "error",
        "@stylistic/no-underscore-dangle": "error",
        "no-unused-expressions": "off",
        "no-var": "error",
        "padded-blocks": [
            "off",
            {
                "blocks": "never"
            },
            {
                "allowSingleLineBlocks": true
            }
        ],
        "prefer-const": "error",
        "quote-props": "off",
        "quotes": "off",
        "react/jsx-curly-spacing": "off",
        "react/jsx-equals-spacing": "off",
        "react/jsx-tag-spacing": [
            "off",
            {
                "afterOpening": "allow",
                "closingSlash": "allow"
            }
        ],
        "react/jsx-wrap-multilines": "off",
        "semi": "off",
        "@stylistic/semi": "error",
        "space-before-function-paren": "off",
        "space-in-parens": [
            "off",
            "never"
        ],
        "@typescript-eslint/tslint/config": [
            "error",
            {
                "rules": {
                    "prefer-method-signature": true
                }
            }
        ],
        "@typescript-eslint/no-unused-vars": [
            "warn",
            {
                "args": "all",
                "argsIgnorePattern": "^_",
                "varsIgnorePattern": "^_",
                "caughtErrorsIgnorePattern": "^_"
            }
        ]
    }
}
